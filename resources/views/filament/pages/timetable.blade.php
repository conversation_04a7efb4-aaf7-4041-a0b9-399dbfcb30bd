<x-filament-panels::page>
    @push('styles')
        @vite(['resources/css/app.css'])
        <style>
            /* Enhanced timetable styles with dark/light mode support */
            :root {
                --timetable-bg-light: #ffffff;
                --timetable-bg-dark: #1f2937;
                --timetable-border-light: #e5e7eb;
                --timetable-border-dark: #374151;
                --timetable-text-light: #374151;
                --timetable-text-dark: #f9fafb;
                --timetable-header-light: #f9fafb;
                --timetable-header-dark: #111827;
                --timetable-shadow-light: rgba(0, 0, 0, 0.1);
                --timetable-shadow-dark: rgba(0, 0, 0, 0.3);
            }

            .timetable-container {
                background: var(--timetable-bg-light);
                border-radius: 12px;
                box-shadow: 0 4px 6px -1px var(--timetable-shadow-light);
                transition: all 0.3s ease;
            }

            .dark .timetable-container {
                background: var(--timetable-bg-dark);
                box-shadow: 0 4px 6px -1px var(--timetable-shadow-dark);
            }

            .schedule-entry {
                position: absolute;
                left: 6px;
                right: 6px;
                border-radius: 10px;
                padding: 10px;
                font-size: 11px;
                overflow: hidden;
                border: 1px solid;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                z-index: 10;
                backdrop-filter: blur(8px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .schedule-entry:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                z-index: 20;
            }

            /* College courses - Enhanced blue theme */
            .schedule-college {
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                border-color: #93c5fd;
                color: #1e40af;
            }

            .dark .schedule-college {
                background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%);
                border-color: rgba(59, 130, 246, 0.4);
                color: #93c5fd;
            }

            /* SHS courses - Enhanced green theme */
            .schedule-shs {
                background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
                border-color: #86efac;
                color: #166534;
            }

            .dark .schedule-shs {
                background: linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(22, 163, 74, 0.3) 100%);
                border-color: rgba(34, 197, 94, 0.4);
                color: #86efac;
            }

            /* Conflict highlighting - Enhanced red theme */
            .schedule-conflict {
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                border-color: #f87171;
                border-width: 2px;
                color: #dc2626;
                animation: pulse-conflict 2s infinite;
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
            }

            .dark .schedule-conflict {
                background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.3) 100%);
                border-color: rgba(248, 113, 113, 0.6);
                color: #fca5a5;
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
            }

            @keyframes pulse-conflict {
                0%, 100% {
                    border-color: #f87171;
                    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
                }
                50% {
                    border-color: #ef4444;
                    box-shadow: 0 0 30px rgba(239, 68, 68, 0.5);
                }
            }

            .dark @keyframes pulse-conflict {
                0%, 100% {
                    border-color: rgba(248, 113, 113, 0.6);
                    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
                }
                50% {
                    border-color: rgba(248, 113, 113, 0.8);
                    box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
                }
            }

            .conflict-indicator {
                position: absolute;
                top: 6px;
                right: 6px;
                width: 14px;
                height: 14px;
                background: radial-gradient(circle, #ef4444 0%, #dc2626 100%);
                border-radius: 50%;
                border: 2px solid #ffffff;
                animation: pulse 2s infinite;
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
            }

            .dark .conflict-indicator {
                border-color: #1f2937;
                box-shadow: 0 2px 8px rgba(239, 68, 68, 0.6);
            }

            /* Loading spinner */
            .timetable-loading {
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 400px;
                flex-direction: column;
                gap: 16px;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid var(--timetable-border-light);
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            .dark .loading-spinner {
                border-color: var(--timetable-border-dark);
                border-top-color: #60a5fa;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Time slot styling */
            .time-slot-row {
                transition: background-color 0.2s ease;
                border-bottom: 1px solid var(--timetable-border-light);
            }

            .dark .time-slot-row {
                border-bottom-color: var(--timetable-border-dark);
            }

            .time-slot-row:hover {
                background-color: rgba(59, 130, 246, 0.05);
            }

            .dark .time-slot-row:hover {
                background-color: rgba(59, 130, 246, 0.1);
            }

            /* Calendar header */
            .calendar-header {
                background: var(--timetable-header-light);
                border-bottom: 2px solid var(--timetable-border-light);
            }

            .dark .calendar-header {
                background: var(--timetable-header-dark);
                border-bottom-color: var(--timetable-border-dark);
            }
        </style>
    @endpush

    <div class="space-y-6" x-data="{
        refreshSelects() {
            // Force refresh of select components
            this.$nextTick(() => {
                this.$dispatch('refresh');
            });
        },
        conflicts: @js($this->getConflicts() ?? []),
        showConflictModal: false,
        showColorLegend: true,

        openConflictModal() {
            this.showConflictModal = true;
            this.$dispatch('open-conflict-modal', { conflicts: this.conflicts });
        },

        getTimeSlotClass(hour) {
            if (hour < 12) return 'time-slot-morning';
            if (hour < 17) return 'time-slot-afternoon';
            return 'time-slot-evening';
        },

        loading: false,
        selectedId: @js($selectedId),
        selectedView: @js($selectedView),

        calculateSchedulePosition(schedule, timeSlots) {
            const startTime = new Date('1970-01-01T' + schedule.start_time);
            const endTime = new Date('1970-01-01T' + schedule.end_time);
            const startHour = startTime.getHours();
            const endHour = endTime.getHours();
            const startMinutes = startTime.getMinutes();
            const endMinutes = endTime.getMinutes();

            // Find the starting slot index
            let startSlotIndex = -1;
            let endSlotIndex = -1;

            timeSlots.forEach((slot, index) => {
                const slotTime = new Date('1970-01-01T' + slot);
                const slotHour = slotTime.getHours();

                if (slotHour === startHour) {
                    startSlotIndex = index;
                }
                if (slotHour === endHour || (slotHour < endHour && timeSlots[index + 1] && new Date('1970-01-01T' + timeSlots[index + 1]).getHours() > endHour)) {
                    endSlotIndex = index;
                }
            });

            if (startSlotIndex === -1) startSlotIndex = 0;
            if (endSlotIndex === -1) endSlotIndex = timeSlots.length - 1;

            const slotHeight = 80; // Height of each time slot in pixels
            const topOffset = startSlotIndex * slotHeight + (startMinutes / 60) * slotHeight;
            const height = ((endSlotIndex - startSlotIndex) * slotHeight) + ((endMinutes - startMinutes) / 60) * slotHeight;

            return {
                top: topOffset + 'px',
                height: Math.max(height, 40) + 'px' // Minimum height of 40px
            };
        },

        showLoading() {
            this.loading = true;
            setTimeout(() => {
                this.loading = false;
            }, 1000); // Simulate loading time
        },

        darkMode: localStorage.getItem('darkMode') === 'true' || false,

        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode);
            if (this.darkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        },

        initDarkMode() {
            if (this.darkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        }
    }"
    @refresh-select-options.window="refreshSelects()"
    @openConflictModal.window="openConflictModal()"
    @downloadFile.window="window.dispatchEvent(new CustomEvent('downloadFile', { detail: $event.detail }))"
    x-init="
        initDarkMode();
        $watch('selectedId', () => showLoading());
        $watch('selectedView', () => showLoading());
    ">

       

        <!-- Conflict Summary Banner -->
        
        {{ $this->form }}

        @if($selectedId)
            <!-- Loading Screen -->
            <div x-show="loading" class="timetable-loading">
                <div class="loading-spinner"></div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Loading schedule data...</div>
            </div>

            <div x-show="!loading" x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 class="grid grid-cols-1 gap-6">

                <!-- Color Legend -->
                <div x-show="showColorLegend"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 transform -translate-y-2"
                     x-transition:enter-end="opacity-100 transform translate-y-0">
                    <x-filament::section>
                        <x-slot name="heading">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Color Legend</span>
                                <button
                                    @click="showColorLegend = false"
                                    class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
                                >
                                    <x-heroicon-o-x-mark class="h-4 w-4" />
                                </button>
                            </div>
                        </x-slot>

                        <div class="color-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #dbeafe; border-color: #bfdbfe;"></div>
                                <span class="legend-label">College Courses</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #dcfce7; border-color: #bbf7d0;"></div>
                                <span class="legend-label">SHS Courses</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #fef2f2; border-color: #fca5a5;"></div>
                                <span class="legend-label">Conflicts</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #fefce8; border-color: #fde047;"></div>
                                <span class="legend-label">Morning (6AM-12PM)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #eff6ff; border-color: #93c5fd;"></div>
                                <span class="legend-label">Afternoon (12PM-5PM)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: #faf5ff; border-color: #c084fc;"></div>
                                <span class="legend-label">Evening (5PM+)</span>
                            </div>
                        </div>
                    </x-filament::section>
                </div>

                <!-- Weekly Calendar View -->
                <div class="timetable-container">
                    <x-filament::section>
                        <x-slot name="heading">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
                                        <span class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                            {{ match($selectedView) {
                                                'room' => 'Room Schedule',
                                                'class' => 'Class Schedule',
                                                'student' => 'Student Schedule',
                                                'course' => 'Course Schedule',
                                                'faculty' => 'Faculty Schedule',
                                                default => 'Schedule View'
                                            } }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $this->getSelectedEntityName() }}
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    @if($this->hasConflicts())
                                        <button
                                            @click="openConflictModal()"
                                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                                        >
                                            <x-heroicon-o-exclamation-triangle class="h-4 w-4 mr-2" />
                                            {{ $this->getConflictCount() }} Conflicts
                                        </button>
                                    @endif
                                    <button
                                        @click="showColorLegend = !showColorLegend"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-sm hover:shadow-md transition-all duration-200"
                                    >
                                        <x-heroicon-o-swatch class="h-4 w-4 mr-2" />
                                        Legend
                                    </button>
                                </div>
                            </div>
                        </x-slot>

                        <div class="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                            <div class="min-w-full relative">
                                <!-- Calendar Header -->
                                <div class="calendar-header grid grid-cols-8 sticky top-0 z-30">
                                    <div class="p-4 border-b border-r border-gray-200 dark:border-gray-700 text-sm font-semibold text-center text-gray-600 dark:text-gray-300">
                                        <div class="flex items-center justify-center space-x-2">
                                            <x-heroicon-o-clock class="h-4 w-4" />
                                            <span>Time</span>
                                        </div>
                                    </div>
                                    @foreach($this->getDays() as $day)
                                        <div class="p-4 border-b border-r border-gray-200 dark:border-gray-700 text-sm font-semibold text-center text-gray-700 dark:text-gray-300">
                                            <div class="flex flex-col items-center space-y-1">
                                                <span>{{ $day }}</span>
                                                <div class="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <!-- Calendar Body -->
                                <div class="relative">
                                    @foreach($this->getTimeSlots() as $index => $timeSlot)
                                        @php
                                            $slotStart = \Carbon\Carbon::parse($timeSlot);
                                            $slotEnd = $slotStart->copy()->addHour();
                                            $isEvenRow = $index % 2 === 0;
                                            $hour = $slotStart->hour;
                                            $timeSlotClass = $hour < 12 ? 'time-slot-morning' : ($hour < 17 ? 'time-slot-afternoon' : 'time-slot-evening');
                                        @endphp
                                        <div class="time-slot-row grid grid-cols-8 {{ $timeSlotClass }}
                                            {{ $isEvenRow ? 'bg-gray-50/30 dark:bg-gray-800/20' : 'bg-white/50 dark:bg-gray-900/20' }}"
                                            style="height: 80px;">
                                            <!-- Time Column -->
                                            <div class="p-4 border-r border-gray-200 dark:border-gray-700 text-sm text-center font-medium text-gray-600 dark:text-gray-400 flex flex-col justify-center">
                                                <div class="text-lg font-semibold">{{ $slotStart->format('g:i') }}</div>
                                                <div class="text-xs text-gray-400 dark:text-gray-500">{{ $slotStart->format('A') }}</div>
                                            </div>

                                            <!-- Days Columns -->
                                            @foreach($this->getDays() as $dayIndex => $day)
                                                <div class="border-r border-gray-200 dark:border-gray-700 relative" style="height: 80px;">
                                                    @if($index === 0)
                                                        <!-- Render all schedules for this day in the first time slot only -->
                                                        @php
                                                            $daySchedules = $schedules->filter(function($schedule) use ($day) {
                                                                return strtolower($schedule->day_of_week) === strtolower($day);
                                                            });
                                                        @endphp

                                                        @foreach($daySchedules as $schedule)
                                                            @php
                                                                $scheduleStart = \Carbon\Carbon::parse($schedule->start_time);
                                                                $scheduleEnd = \Carbon\Carbon::parse($schedule->end_time);

                                                                // Calculate position and height based on time
                                                                $timeSlots = $this->getTimeSlots();
                                                                $startSlotIndex = 0;
                                                                $endSlotIndex = count($timeSlots) - 1;

                                                                // Find exact slot positions
                                                                foreach($timeSlots as $slotIndex => $slot) {
                                                                    $slotTime = \Carbon\Carbon::parse($slot);
                                                                    if ($slotTime->hour <= $scheduleStart->hour) {
                                                                        $startSlotIndex = $slotIndex;
                                                                    }
                                                                    if ($slotTime->hour < $scheduleEnd->hour) {
                                                                        $endSlotIndex = $slotIndex + 1;
                                                                    }
                                                                }

                                                                // Calculate precise positioning
                                                                $slotHeight = 80; // Height of each time slot
                                                                $startMinuteOffset = ($scheduleStart->minute / 60) * $slotHeight;
                                                                $endMinuteOffset = ($scheduleEnd->minute / 60) * $slotHeight;

                                                                $topPosition = ($startSlotIndex * $slotHeight) + $startMinuteOffset;
                                                                $scheduleHeight = (($endSlotIndex - $startSlotIndex) * $slotHeight) + $endMinuteOffset - $startMinuteOffset;

                                                                // Minimum height for readability
                                                                $scheduleHeight = max($scheduleHeight, 40);

                                                                // Get color coding and conflict status
                                                                $hasConflict = $this->scheduleHasConflict($schedule);
                                                                $conflictService = app(\App\Services\TimetableConflictService::class);
                                                                $colorClass = $conflictService->getScheduleCssClass($schedule, $hasConflict);

                                                            // Build tooltip content
                                                            $tooltipContent = [];
                                                            $tooltipContent[] = $schedule->class?->subject?->title ?? 'N/A';
                                                            $tooltipContent[] = "Section: " . ($schedule->class?->section ?? 'N/A');
                                                            $tooltipContent[] = "Time: {$scheduleStart->format('H:i')} - {$scheduleEnd->format('H:i')}";

                                                            if($selectedView !== 'class') {
                                                                $tooltipContent[] = "Subject Code: " . ($schedule->class?->subject?->code ?? 'N/A');
                                                            }

                                                            if($selectedView !== 'room') {
                                                                $tooltipContent[] = "Room: " . ($schedule->room?->name ?? 'N/A');
                                                            }

                                                            if($selectedView !== 'faculty') {
                                                                $tooltipContent[] = "Faculty: " . ($schedule->class?->faculty?->full_name ?? 'N/A');
                                                            }

                                                            // Add course information for relevant views
                                                            if(in_array($selectedView, ['room', 'student', 'faculty']) && $schedule->class) {
                                                                $courseCodes = $schedule->class->formatted_course_codes;
                                                                if($courseCodes && $courseCodes !== 'N/A') {
                                                                    $tooltipContent[] = "Course(s): " . $courseCodes;
                                                                }
                                                            }

                                                            // Add classification for course and faculty views
                                                            if(in_array($selectedView, ['course', 'faculty']) && $schedule->class) {
                                                                $classification = $schedule->class->classification ?: 'college';
                                                                $tooltipContent[] = "Type: " . ucfirst($classification);
                                                            }

                                                            if($selectedView === 'student') {
                                                                // Add enrolled students count for student view
                                                                $enrolledCount = $schedule->class ? $schedule->class->class_enrollments->count() : 0;
                                                                $tooltipContent[] = "Enrolled Students: {$enrolledCount}";
                                                            }

                                                                // Build enhanced tooltip content
                                                                $tooltipContent = [];
                                                                $tooltipContent[] = '<strong>' . ($schedule->class?->subject?->title ?? 'N/A') . '</strong>';
                                                                $tooltipContent[] = "Section: " . ($schedule->class?->section ?? 'N/A');
                                                                $tooltipContent[] = "Time: {$scheduleStart->format('g:i A')} - {$scheduleEnd->format('g:i A')}";
                                                                $tooltipContent[] = "Duration: " . $scheduleStart->diffInHours($scheduleEnd) . "h " . ($scheduleStart->diffInMinutes($scheduleEnd) % 60) . "m";

                                                                if($selectedView !== 'class') {
                                                                    $tooltipContent[] = "Subject Code: " . ($schedule->class?->subject?->code ?? 'N/A');
                                                                }

                                                                if($selectedView !== 'room') {
                                                                    $tooltipContent[] = "Room: " . ($schedule->room?->name ?? 'N/A');
                                                                }

                                                                if($selectedView !== 'faculty') {
                                                                    $tooltipContent[] = "Faculty: " . ($schedule->class?->faculty?->full_name ?? 'N/A');
                                                                }

                                                                // Add course information for relevant views
                                                                if(in_array($selectedView, ['room', 'student', 'faculty']) && $schedule->class) {
                                                                    $courseCodes = $schedule->class->formatted_course_codes;
                                                                    if($courseCodes && $courseCodes !== 'N/A') {
                                                                        $tooltipContent[] = "Course(s): " . $courseCodes;
                                                                    }
                                                                }

                                                                // Add classification for course and faculty views
                                                                if(in_array($selectedView, ['course', 'faculty']) && $schedule->class) {
                                                                    $classification = $schedule->class->classification ?: 'college';
                                                                    $tooltipContent[] = "Type: " . ucfirst($classification);
                                                                }

                                                                if($selectedView === 'student') {
                                                                    // Add enrolled students count for student view
                                                                    $enrolledCount = $schedule->class ? $schedule->class->class_enrollments->count() : 0;
                                                                    $tooltipContent[] = "Enrolled Students: {$enrolledCount}";
                                                                }

                                                                // Add conflict warning if applicable
                                                                if($hasConflict) {
                                                                    $tooltipContent[] = "<strong style='color: #dc2626;'>⚠️ CONFLICT DETECTED</strong>";
                                                                }

                                                                $tooltipHtml = implode('<br>', $tooltipContent);
                                                            @endphp

                                                            <div
                                                                class="schedule-entry {{ $colorClass }}"
                                                                style="
                                                                    position: absolute;
                                                                    top: {{ $topPosition }}px;
                                                                    height: {{ $scheduleHeight }}px;
                                                                    left: 8px;
                                                                    right: 8px;
                                                                    z-index: {{ $hasConflict ? 50 : 20 }};
                                                                "
                                                                onclick="window.open('{{ $schedule->class ? route('filament.admin.resources.classes.view', ['record' => $schedule->class_id]) : '#' }}', '_blank')"
                                                                x-data="{}"
                                                                x-tooltip.html
                                                                x-tooltip.raw="{{ $tooltipHtml }}"
                                                                @if($hasConflict)
                                                                    @click.stop="openConflictModal()"
                                                                @endif
                                                            >
                                                                <!-- Conflict indicator -->
                                                                @if($hasConflict)
                                                                    <div class="conflict-indicator"></div>
                                                                @endif

                                                                <!-- Classification badge -->
                                                                @php
                                                                    $classification = $schedule->class?->classification ?: 'college';
                                                                @endphp
                                                                <div class="absolute top-2 right-2">
                                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-[0.65rem] font-semibold shadow-sm"
                                                                          style="
                                                                              background-color: {{ $classification === 'shs' ? '#dcfce7' : '#dbeafe' }};
                                                                              color: {{ $classification === 'shs' ? '#166534' : '#1e40af' }};
                                                                              border: 1px solid {{ $classification === 'shs' ? '#bbf7d0' : '#bfdbfe' }};
                                                                          ">
                                                                        {{ strtoupper($classification) }}
                                                                    </span>
                                                                </div>

                                                                <!-- Schedule content -->
                                                                <div class="flex flex-col h-full justify-between">
                                                                    <div class="flex-1">
                                                                        <div class="schedule-title text-sm font-semibold mb-1 leading-tight">
                                                                            {{ $schedule->class?->subject?->title ?? 'N/A' }}
                                                                        </div>
                                                                        <div class="schedule-details text-xs opacity-90 mb-1">
                                                                            Section {{ $schedule->class?->section ?? 'N/A' }}
                                                                        </div>
                                                                        @if($selectedView !== 'room')
                                                                            <div class="schedule-details text-xs opacity-80 truncate">
                                                                                📍 {{ $schedule->room?->name ?? 'N/A' }}
                                                                            </div>
                                                                        @endif
                                                                        @if($selectedView !== 'faculty')
                                                                            <div class="schedule-details text-xs opacity-80 truncate">
                                                                                👨‍🏫 {{ $schedule->class?->faculty?->full_name ?? 'N/A' }}
                                                                            </div>
                                                                        @endif
                                                                    </div>

                                                                    <div class="border-t border-white/20 pt-1 mt-1">
                                                                        <div class="schedule-details text-xs font-medium opacity-90">
                                                                            🕐 {{ $scheduleStart->format('g:i A') }} - {{ $scheduleEnd->format('g:i A') }}
                                                                        </div>
                                                                        @if($scheduleHeight > 60)
                                                                            <div class="schedule-details text-xs opacity-75">
                                                                                ⏱️ {{ $scheduleStart->diffInHours($scheduleEnd) }}h {{ $scheduleStart->diffInMinutes($scheduleEnd) % 60 }}m
                                                                            </div>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                            @endforeach
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </x-filament::section>
                </div>

                <!-- List View -->
                <div>
                    <x-filament::section>
                        <x-slot name="heading">Schedule List View</x-slot>
                        {{ $this->table }}
                    </x-filament::section>
                </div>
            </div>
        @else
            <x-filament::section>
                <div class="flex items-center justify-center py-12">
                    <div class="text-center">
                        <x-filament::icon
                            icon="heroicon-o-calendar"
                            class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500"
                        />
                        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No schedule selected</h3>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Please select a {{ strtolower($this->getSecondSelectLabel()) }} to view its schedule
                        </p>
                    </div>
                </div>
            </x-filament::section>
        @endif

        <!-- Conflict Analysis Modal -->
        <x-timetable-conflict-modal :conflicts="$this->getConflicts() ?? []" />
    </div>

    @push('scripts')
        <script>
            // Handle conflict modal events
            document.addEventListener('alpine:init', () => {
                Alpine.data('timetableConflicts', () => ({
                    init() {
                        this.$watch('conflicts', (value) => {
                            if (value && Object.keys(value).length > 0) {
                                console.log('Conflicts detected:', value);
                            }
                        });
                    }
                }));
            });

            // Handle conflict resolution events
            window.addEventListener('resolveConflict', (event) => {
                console.log('Resolving conflict:', event.detail);
                // Add conflict resolution logic here
            });

            window.addEventListener('viewConflictingSchedule', (event) => {
                console.log('Viewing conflicting schedule:', event.detail);
                // Add schedule viewing logic here
            });

            // Handle file download events
            window.addEventListener('downloadFile', (event) => {
                const { url, filename } = event.detail;

                // Create a temporary anchor element
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;
                link.style.display = 'none';

                // Add to DOM, click, and remove
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        </script>
    @endpush
</x-filament-panels::page>
