var x=class{constructor(t,i,e,n){if(isNaN(t)||isNaN(i))throw new Error(`Point is invalid: (${t}, ${i})`);this.x=+t,this.y=+i,this.pressure=e||0,this.time=n||Date.now()}distanceTo(t){return Math.sqrt(Math.pow(this.x-t.x,2)+Math.pow(this.y-t.y,2))}equals(t){return this.x===t.x&&this.y===t.y&&this.pressure===t.pressure&&this.time===t.time}velocityFrom(t){return this.time!==t.time?this.distanceTo(t)/(this.time-t.time):0}},P=class f{constructor(t,i,e,n,s,h){this.startPoint=t,this.control2=i,this.control1=e,this.endPoint=n,this.startWidth=s,this.endWidth=h}static fromPoints(t,i){let e=this.calculateControlPoints(t[0],t[1],t[2]).c2,n=this.calculateControlPoints(t[1],t[2],t[3]).c1;return new f(t[1],e,n,t[2],i.start,i.end)}static calculateControlPoints(t,i,e){let n=t.x-i.x,s=t.y-i.y,h=i.x-e.x,d=i.y-e.y,r={x:(t.x+i.x)/2,y:(t.y+i.y)/2},o={x:(i.x+e.x)/2,y:(i.y+e.y)/2},l=Math.sqrt(n*n+s*s),c=Math.sqrt(h*h+d*d),v=r.x-o.x,_=r.y-o.y,g=c/(l+c),p={x:o.x+v*g,y:o.y+_*g},a=i.x-p.x,u=i.y-p.y;return{c1:new x(r.x+a,r.y+u),c2:new x(o.x+a,o.y+u)}}length(){let i=0,e,n;for(let s=0;s<=10;s+=1){let h=s/10,d=this.point(h,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),r=this.point(h,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(s>0){let o=d-e,l=r-n;i+=Math.sqrt(o*o+l*l)}e=d,n=r}return i}point(t,i,e,n,s){return i*(1-t)*(1-t)*(1-t)+3*e*(1-t)*(1-t)*t+3*n*(1-t)*t*t+s*t*t*t}},E=class{constructor(){try{this._et=new EventTarget}catch{this._et=document}}addEventListener(t,i,e){this._et.addEventListener(t,i,e)}dispatchEvent(t){return this._et.dispatchEvent(t)}removeEventListener(t,i,e){this._et.removeEventListener(t,i,e)}};function S(f,t=250){let i=0,e=null,n,s,h,d=()=>{i=Date.now(),e=null,n=f.apply(s,h),e||(s=null,h=[])};return function(...o){let l=Date.now(),c=t-(l-i);return s=this,h=o,c<=0||c>t?(e&&(clearTimeout(e),e=null),i=l,n=f.apply(s,h),e||(s=null,h=[])):e||(e=window.setTimeout(d,c)),n}}var y=class f extends E{constructor(t,i={}){super(),this.canvas=t,this._drawningStroke=!1,this._isEmpty=!0,this._lastPoints=[],this._data=[],this._lastVelocity=0,this._lastWidth=0,this._handleMouseDown=e=>{e.buttons===1&&(this._drawningStroke=!0,this._strokeBegin(e))},this._handleMouseMove=e=>{this._drawningStroke&&this._strokeMoveUpdate(e)},this._handleMouseUp=e=>{e.buttons===1&&this._drawningStroke&&(this._drawningStroke=!1,this._strokeEnd(e))},this._handleTouchStart=e=>{if(e.cancelable&&e.preventDefault(),e.targetTouches.length===1){let n=e.changedTouches[0];this._strokeBegin(n)}},this._handleTouchMove=e=>{e.cancelable&&e.preventDefault();let n=e.targetTouches[0];this._strokeMoveUpdate(n)},this._handleTouchEnd=e=>{if(e.target===this.canvas){e.cancelable&&e.preventDefault();let s=e.changedTouches[0];this._strokeEnd(s)}},this._handlePointerStart=e=>{this._drawningStroke=!0,e.preventDefault(),this._strokeBegin(e)},this._handlePointerMove=e=>{this._drawningStroke&&(e.preventDefault(),this._strokeMoveUpdate(e))},this._handlePointerEnd=e=>{this._drawningStroke&&(e.preventDefault(),this._drawningStroke=!1,this._strokeEnd(e))},this.velocityFilterWeight=i.velocityFilterWeight||.7,this.minWidth=i.minWidth||.5,this.maxWidth=i.maxWidth||2.5,this.throttle="throttle"in i?i.throttle:16,this.minDistance="minDistance"in i?i.minDistance:5,this.dotSize=i.dotSize||0,this.penColor=i.penColor||"black",this.backgroundColor=i.backgroundColor||"rgba(0,0,0,0)",this.compositeOperation=i.compositeOperation||"source-over",this._strokeMoveUpdate=this.throttle?S(f.prototype._strokeUpdate,this.throttle):f.prototype._strokeUpdate,this._ctx=t.getContext("2d"),this.clear(),this.on()}clear(){let{_ctx:t,canvas:i}=this;t.fillStyle=this.backgroundColor,t.clearRect(0,0,i.width,i.height),t.fillRect(0,0,i.width,i.height),this._data=[],this._reset(this._getPointGroupOptions()),this._isEmpty=!0}fromDataURL(t,i={}){return new Promise((e,n)=>{let s=new Image,h=i.ratio||window.devicePixelRatio||1,d=i.width||this.canvas.width/h,r=i.height||this.canvas.height/h,o=i.xOffset||0,l=i.yOffset||0;this._reset(this._getPointGroupOptions()),s.onload=()=>{this._ctx.drawImage(s,o,l,d,r),e()},s.onerror=c=>{n(c)},s.crossOrigin="anonymous",s.src=t,this._isEmpty=!1})}toDataURL(t="image/png",i){switch(t){case"image/svg+xml":return typeof i!="object"&&(i=void 0),`data:image/svg+xml;base64,${btoa(this.toSVG(i))}`;default:return typeof i!="number"&&(i=void 0),this.canvas.toDataURL(t,i)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";let t=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!t?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerStart),this.canvas.removeEventListener("pointermove",this._handlePointerMove),this.canvas.ownerDocument.removeEventListener("pointerup",this._handlePointerEnd),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("mousemove",this._handleMouseMove),this.canvas.ownerDocument.removeEventListener("mouseup",this._handleMouseUp),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this.canvas.removeEventListener("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(t,{clear:i=!0}={}){i&&this.clear(),this._fromData(t,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(t)}toData(){return this._data}_getPointGroupOptions(t){return{penColor:t&&"penColor"in t?t.penColor:this.penColor,dotSize:t&&"dotSize"in t?t.dotSize:this.dotSize,minWidth:t&&"minWidth"in t?t.minWidth:this.minWidth,maxWidth:t&&"maxWidth"in t?t.maxWidth:this.maxWidth,velocityFilterWeight:t&&"velocityFilterWeight"in t?t.velocityFilterWeight:this.velocityFilterWeight,compositeOperation:t&&"compositeOperation"in t?t.compositeOperation:this.compositeOperation}}_strokeBegin(t){this.dispatchEvent(new CustomEvent("beginStroke",{detail:t}));let i=this._getPointGroupOptions(),e=Object.assign(Object.assign({},i),{points:[]});this._data.push(e),this._reset(i),this._strokeUpdate(t)}_strokeUpdate(t){if(this._data.length===0){this._strokeBegin(t);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:t}));let i=t.clientX,e=t.clientY,n=t.pressure!==void 0?t.pressure:t.force!==void 0?t.force:0,s=this._createPoint(i,e,n),h=this._data[this._data.length-1],d=h.points,r=d.length>0&&d[d.length-1],o=r?s.distanceTo(r)<=this.minDistance:!1,l=this._getPointGroupOptions(h);if(!r||!(r&&o)){let c=this._addPoint(s,l);r?c&&this._drawCurve(c,l):this._drawDot(s,l),d.push({time:s.time,x:s.x,y:s.y,pressure:s.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:t}))}_strokeEnd(t){this._strokeUpdate(t),this.dispatchEvent(new CustomEvent("endStroke",{detail:t}))}_handlePointerEvents(){this._drawningStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerStart),this.canvas.addEventListener("pointermove",this._handlePointerMove),this.canvas.ownerDocument.addEventListener("pointerup",this._handlePointerEnd)}_handleMouseEvents(){this._drawningStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown),this.canvas.addEventListener("mousemove",this._handleMouseMove),this.canvas.ownerDocument.addEventListener("mouseup",this._handleMouseUp)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart),this.canvas.addEventListener("touchmove",this._handleTouchMove),this.canvas.addEventListener("touchend",this._handleTouchEnd)}_reset(t){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(t.minWidth+t.maxWidth)/2,this._ctx.fillStyle=t.penColor,this._ctx.globalCompositeOperation=t.compositeOperation}_createPoint(t,i,e){let n=this.canvas.getBoundingClientRect();return new x(t-n.left,i-n.top,e,new Date().getTime())}_addPoint(t,i){let{_lastPoints:e}=this;if(e.push(t),e.length>2){e.length===3&&e.unshift(e[0]);let n=this._calculateCurveWidths(e[1],e[2],i),s=P.fromPoints(e,n);return e.shift(),s}return null}_calculateCurveWidths(t,i,e){let n=e.velocityFilterWeight*i.velocityFrom(t)+(1-e.velocityFilterWeight)*this._lastVelocity,s=this._strokeWidth(n,e),h={end:s,start:this._lastWidth};return this._lastVelocity=n,this._lastWidth=s,h}_strokeWidth(t,i){return Math.max(i.maxWidth/(t+1),i.minWidth)}_drawCurveSegment(t,i,e){let n=this._ctx;n.moveTo(t,i),n.arc(t,i,e,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(t,i){let e=this._ctx,n=t.endWidth-t.startWidth,s=Math.ceil(t.length())*2;e.beginPath(),e.fillStyle=i.penColor;for(let h=0;h<s;h+=1){let d=h/s,r=d*d,o=r*d,l=1-d,c=l*l,v=c*l,_=v*t.startPoint.x;_+=3*c*d*t.control1.x,_+=3*l*r*t.control2.x,_+=o*t.endPoint.x;let g=v*t.startPoint.y;g+=3*c*d*t.control1.y,g+=3*l*r*t.control2.y,g+=o*t.endPoint.y;let p=Math.min(t.startWidth+o*n,i.maxWidth);this._drawCurveSegment(_,g,p)}e.closePath(),e.fill()}_drawDot(t,i){let e=this._ctx,n=i.dotSize>0?i.dotSize:(i.minWidth+i.maxWidth)/2;e.beginPath(),this._drawCurveSegment(t.x,t.y,n),e.closePath(),e.fillStyle=i.penColor,e.fill()}_fromData(t,i,e){for(let n of t){let{points:s}=n,h=this._getPointGroupOptions(n);if(s.length>1)for(let d=0;d<s.length;d+=1){let r=s[d],o=new x(r.x,r.y,r.pressure,r.time);d===0&&this._reset(h);let l=this._addPoint(o,h);l&&i(l,h)}else this._reset(h),e(s[0],h)}}toSVG({includeBackgroundColor:t=!1}={}){let i=this._data,e=Math.max(window.devicePixelRatio||1,1),n=0,s=0,h=this.canvas.width/e,d=this.canvas.height/e,r=document.createElementNS("http://www.w3.org/2000/svg","svg");if(r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),r.setAttribute("viewBox",`${n} ${s} ${h} ${d}`),r.setAttribute("width",h.toString()),r.setAttribute("height",d.toString()),t&&this.backgroundColor){let o=document.createElement("rect");o.setAttribute("width","100%"),o.setAttribute("height","100%"),o.setAttribute("fill",this.backgroundColor),r.appendChild(o)}return this._fromData(i,(o,{penColor:l})=>{let c=document.createElement("path");if(!isNaN(o.control1.x)&&!isNaN(o.control1.y)&&!isNaN(o.control2.x)&&!isNaN(o.control2.y)){let v=`M ${o.startPoint.x.toFixed(3)},${o.startPoint.y.toFixed(3)} C ${o.control1.x.toFixed(3)},${o.control1.y.toFixed(3)} ${o.control2.x.toFixed(3)},${o.control2.y.toFixed(3)} ${o.endPoint.x.toFixed(3)},${o.endPoint.y.toFixed(3)}`;c.setAttribute("d",v),c.setAttribute("stroke-width",(o.endWidth*2.25).toFixed(3)),c.setAttribute("stroke",l),c.setAttribute("fill","none"),c.setAttribute("stroke-linecap","round"),r.appendChild(c)}},(o,{penColor:l,dotSize:c,minWidth:v,maxWidth:_})=>{let g=document.createElement("circle"),p=c>0?c:(v+_)/2;g.setAttribute("r",p.toString()),g.setAttribute("cx",o.x.toString()),g.setAttribute("cy",o.y.toString()),g.setAttribute("fill",l),r.appendChild(g)}),r.outerHTML}};var W=({backgroundColor:f,backgroundColorOnDark:t,confirmable:i,disabled:e,dotSize:n,exportBackgroundColor:s,exportPenColor:h,filename:d,maxWidth:r,minDistance:o,minWidth:l,penColor:c,penColorOnDark:v,state:_,throttle:g,velocityFilterWeight:p})=>({state:_,previousState:_,dirty:!1,confirmed:!1,signaturePad:null,init(){this.signaturePad=new y(this.$refs.canvas,{backgroundColor:f,dotSize:n,maxWidth:r,minDistance:o,minWidth:l,penColor:c,throttle:g,velocityFilterWeight:p}),e&&this.signaturePad.off(),this.watchState(),this.watchResize(),this.watchTheme(),_.initialValue&&(this.signaturePad.fromDataURL(_.initialValue),this.signaturePad.addEventListener("beginStroke",()=>{this.signaturePad.clear()},{once:!0}))},clear(){this.signaturePad.clear(),this.state=null,this.confirmed=!1,this.dirty=!1,this.signaturePad.on()},undo(){let a=this.signaturePad.toData();a.length&&(a.pop(),this.signaturePad.fromData(a)),a.length||(this.state=null),this.confirmed=!1,this.dirty=a.length>0,this.signaturePad.on()},done(){let{data:a,canvasBackgroundColor:u,canvasPenColor:m}=this.prepareToExport();this.signaturePad.fromData(a),this.previousState=this.state,this.state=this.signaturePad.toDataURL(),i&&(this.confirmed=!0,this.signaturePad.off());let{data:w}=this.restoreFromExport(a,u,m);this.signaturePad.fromData(w)},downloadAs(a,u){let{data:m,canvasBackgroundColor:w,canvasPenColor:k}=this.prepareToExport();this.signaturePad.fromData(m),this.download(this.signaturePad.toDataURL(a,{includeBackgroundColor:!0}),`${d}.${u}`);let{data:C}=this.restoreFromExport(m,w,k);this.signaturePad.fromData(C)},watchState(){this.signaturePad.addEventListener("endStroke",a=>{this.dirty=!0,!i&&this.done()},{once:!1}),this.$watch("confirmed",a=>{i&&!a&&(this.state=null)})},watchResize(){window.addEventListener("resize",()=>this.resizeCanvas),this.resizeCanvas()},resizeCanvas(){let a=Math.max(window.devicePixelRatio||1,1);this.$refs.canvas.width=this.$refs.canvas.offsetWidth*a,this.$refs.canvas.height=this.$refs.canvas.offsetHeight*a,this.$refs.canvas.getContext("2d").scale(a,a),this.signaturePad.clear()},watchTheme(){let a;this.$store.hasOwnProperty("theme")?(window.addEventListener("theme-changed",u=>this.onThemeChanged(u.detail)),a=this.$store.theme):(window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",u=>this.onThemeChanged(u.matches?"dark":"light")),a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),this.onThemeChanged(a)},onThemeChanged(a){if(this.signaturePad.penColor=a==="dark"?v??c:c,this.signaturePad.backgroundColor=a==="dark"?t??f:f,!this.signaturePad.toData().length)return;let u=this.signaturePad.toData();u.map(m=>(m.penColor=a==="dark"?v??c:c,m.backgroundColor=a==="dark"?t??f:f,m)),this.signaturePad.clear(),this.signaturePad.fromData(u)},prepareToExport(){let a=this.signaturePad.toData(),u=this.signaturePad.backgroundColor,m=this.signaturePad.penColor;return this.signaturePad.backgroundColor=s??this.signaturePad.backgroundColor,a.map(w=>w.penColor=h??w.penColor),{data:a,canvasBackgroundColor:u,canvasPenColor:m}},restoreFromExport(a,u,m){return this.signaturePad.backgroundColor=u,a.map(w=>w.penColor=m),{data:a}},download(a,u){let m=document.createElement("a");m.download=u,m.href=a,document.body.appendChild(m),m.click(),document.body.removeChild(m)}});export{W as default};
/*! Bundled license information:

signature_pad/dist/signature_pad.js:
  (*!
   * Signature Pad v4.1.6 | https://github.com/szimek/signature_pad
   * (c) 2023 Szymon Nowak | Released under the MIT license
   *)
*/
