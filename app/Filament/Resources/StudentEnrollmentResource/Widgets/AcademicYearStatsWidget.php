<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentEnrollmentResource\Widgets;

use App\Models\GeneralSetting; // Import GeneralSetting
use App\Models\StudentEnrollment; // Keep only one import
use App\Services\GeneralSettingsService;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Cache; // Import Cache
use Illuminate\Support\Facades\DB;

// Add this import

final class AcademicYearStatsWidget extends ChartWidget
{
    // protected int | string | array $columnSpan = 'full';

    public ?string $filter = 'all';

    protected static ?string $heading = 'Enrollment Distribution';

    protected static ?string $maxHeight = '500px';

    protected function getFilters(): ?array
    {
        return [
            'all' => 'All Courses',
            'BSIT' => 'BSIT',
            'BSHM' => 'BSHM',
            'BSBA' => 'BSBA',
        ];
    }

    protected function getData(): array
    {
        // Fetch current academic period settings using the service
        $settingsService = app(GeneralSettingsService::class);
        $settingsService->getCurrentSchoolYearString();
        $settingsService->getCurrentSemester();

        // Base query with academic period scope
        $baseQuery = StudentEnrollment::query()
            ->currentAcademicPeriod(); // Apply the scope
        // ->withTrashed(); // Decide if you want to include trashed records in stats

        if ($this->filter === 'all') {
            // For all courses, show total students per course for the current period
            $data = (clone $baseQuery) // Clone the scoped base query
                ->select([
                    DB::raw('COUNT(*) as count'),
                    DB::raw('CASE
                        WHEN EXISTS (
                            SELECT 1 FROM students s
                            INNER JOIN courses c ON s.course_id = c.id
                            WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                            AND c.code LIKE \'BSIT%\'
                        ) THEN \'BSIT\'
                        WHEN EXISTS (
                            SELECT 1 FROM students s
                            INNER JOIN courses c ON s.course_id = c.id
                            WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                            AND c.code LIKE \'BSHM%\'
                        ) THEN \'BSHM\'
                        WHEN EXISTS (
                            SELECT 1 FROM students s
                            INNER JOIN courses c ON s.course_id = c.id
                            WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                            AND c.code LIKE \'BSBA%\'
                        ) THEN \'BSBA\'
                        ELSE \'Others\'
                    END as course'),
                ])
                ->groupBy(DB::raw('CASE 
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSIT%\'
                    ) THEN \'BSIT\'
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSHM%\'
                    ) THEN \'BSHM\'
                    WHEN EXISTS (
                        SELECT 1 FROM students s
                        INNER JOIN courses c ON s.course_id = c.id
                        WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                        AND c.code LIKE \'BSBA%\'
                    ) THEN \'BSBA\'
                    ELSE \'Others\'
                END'))
                ->get()
                ->mapWithKeys(function ($item) {
                    $courseLabel = match ($item->course) {
                        'BSIT' => 'BS Information Technology',
                        'BSHM' => 'BS Hospitality Management',
                        'BSBA' => 'BS Business Administration',
                        default => 'Others'
                    };

                    return [$courseLabel => $item->count];
                })
                ->toArray();

            $labels = array_keys($data);
            $values = array_values($data);
            $colors = [
                'rgb(54, 162, 235)',   // BSIT - Blue
                'rgb(255, 99, 132)',   // BSHM - Red
                'rgb(255, 206, 86)',   // BSBA - Yellow
                'rgb(75, 192, 192)',   // Others - Green
            ];
        } else {
            // For specific course, show academic year breakdown for the current period
            $selectedCourse = $this->filter;
            $data = (clone $baseQuery) // Clone the scoped base query
                ->select([
                    DB::raw('COUNT(*) as count'),
                    'academic_year',
                ])
                ->whereRaw('EXISTS (
                    SELECT 1 FROM students s
                    INNER JOIN courses c ON s.course_id = c.id
                    WHERE CAST(student_enrollment.student_id AS BIGINT) = s.id -- Use BIGINT
                    AND c.code LIKE ?
                )', [$selectedCourse.'%'])
                ->groupBy('academic_year')
                ->orderBy('academic_year')
                ->get()
                ->pluck('count', 'academic_year')
                ->toArray();

            // Initialize all academic years with 0
            $yearLabels = [
                '1' => '1st Year',
                '2' => '2nd Year',
                '3' => '3rd Year',
                '4' => '4th Year',
            ];

            // Fill in the data array with actual counts or 0 for missing years
            $filledData = array_fill_keys(array_values($yearLabels), 0);
            foreach ($data as $year => $count) {
                if (isset($yearLabels[$year])) {
                    $filledData[$yearLabels[$year]] = $count;
                }
            }

            $labels = array_keys($filledData);
            $values = array_values($filledData);
            $colors = [
                'rgb(255, 99, 132)',   // 1st Year - Red
                'rgb(54, 162, 235)',   // 2nd Year - Blue
                'rgb(255, 206, 86)',   // 3rd Year - Yellow
                'rgb(75, 192, 192)',   // 4th Year - Green
            ];
        }

        return [
            'datasets' => [
                [
                    'label' => $this->filter === 'all' ? 'Students per Course' : 'Students per Year',
                    'data' => $values,
                    'backgroundColor' => array_slice($colors, 0, count($values)),
                    'borderColor' => array_slice($colors, 0, count($values)),
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        $title = $this->filter === 'all' ? 'Total Students per Course' : 'Academic Year Distribution - '.match ($this->filter) {
            'BSIT' => 'BS Information Technology',
            'BSHM' => 'BS Hospitality Management',
            'BSBA' => 'BS Business Administration',
            default => 'Unknown Course'
        };

        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'enabled' => true,
                    'intersect' => false,
                    'mode' => 'nearest',
                ],
                'title' => [
                    'display' => true,
                    'text' => $title,
                    'font' => [
                        'size' => 16,
                        'weight' => 'bold',
                    ],
                    'padding' => 20,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'precision' => 0,
                    ],
                    'title' => [
                        'display' => true,
                        'text' => 'Number of Students',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
                'x' => [
                    'title' => [
                        'display' => true,
                        'text' => $this->filter === 'all' ? 'Courses' : 'Academic Year',
                        'font' => [
                            'weight' => 'bold',
                        ],
                    ],
                ],
            ],
            'maintainAspectRatio' => false,
            'responsive' => true,
        ];
    }
}
